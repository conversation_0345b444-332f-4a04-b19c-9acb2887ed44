{"name": "imponeer/data-filter", "description": "Replacement for ImpressCMS DataFilter class", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON> (aka MekDrop)", "email": "<EMAIL>"}], "require": {"php": "^8.3", "ext-curl": "*", "ext-mbstring": "*", "cerdic/css-tidy": "^2.2", "ezyang/htmlpurifier": "^4.18", "guzzlehttp/guzzle": "^7.9", "psr/container": "^2.0", "psr/event-dispatcher": "^1.0", "scrivo/highlight.php": "^9.18"}, "autoload": {"psr-4": {"Imponeer\\DataFilter\\": "src/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true}, "autoload-dev": {"psr-4": {"Imponeer\\DataFilter\\Tests\\": "tests/"}}, "require-dev": {"league/event": "^3.0", "phpunit/phpunit": "^12"}, "scripts": {"test": "phpunit"}}