<?php

declare(strict_types=1);

namespace Imponeer\DataFilter\Tests;

use Generator;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Imponeer\DataFilter\StopSpammer;
use GuzzleHttp\Client;
use <PERSON>uz<PERSON><PERSON>ttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;

class StopSpammerTest extends TestCase
{
    private function createStopSpammerWithMockedResponse(string $appears, callable $assertRequest = null): StopSpammer
    {
        $mock = new MockHandler([
            function ($request, $options) use ($appears, $assertRequest) {
                if ($assertRequest) {
                    $assertRequest($request, $options);
                }
                return new Response(200, [], "<appears>{$appears}</appears>");
            }
        ]);
        $handlerStack = HandlerStack::create($mock);
        $client = new Client(['handler' => $handlerStack]);
        return new StopSpammer($client);
    }

    public static function provideBadUsernameEmailIpData(): Generator
    {
        yield 'username spammer' => [
            'method' => 'badUsername',
            'input' => 'spammer',
            'appears' => 'yes',
            'expected' => true,
            'assertString' => 'username=spammer',
        ];
        yield 'username gooduser' => [
            'method' => 'badUsername',
            'input' => 'gooduser',
            'appears' => 'no',
            'expected' => false,
            'assertString' => 'username=gooduser',
        ];
        yield 'email spam' => [
            'method' => 'badEmail',
            'input' => '<EMAIL>',
            'appears' => 'yes',
            'expected' => true,
            'assertString' => 'email=spam%40example.com',
        ];
        yield 'email good' => [
            'method' => 'badEmail',
            'input' => '<EMAIL>',
            'appears' => 'no',
            'expected' => false,
            'assertString' => 'email=good%40example.com',
        ];
        yield 'ip spam' => [
            'method' => 'badIP',
            'input' => '*******',
            'appears' => 'yes',
            'expected' => true,
            'assertString' => 'ip=*******',
        ];
        yield 'ip good' => [
            'method' => 'badIP',
            'input' => '*******',
            'appears' => 'no',
            'expected' => false,
            'assertString' => 'ip=*******',
        ];
    }

    #[DataProvider('provideBadUsernameEmailIpData')]
    public function testBadUsernameEmailIpCases(string $method, string $input, string $appears, bool $expected, string $assertString): void
    {
        $stopSpammer = $this->createStopSpammerWithMockedResponse($appears, function($request) use ($assertString) {
            $this->assertStringContainsString($assertString, (string)$request->getUri());
        });
        $this->assertSame($expected, $stopSpammer->$method($input));
    }

    #[DataProvider('provideBadIpData')]
    public function testBadIPSpecialCases(string $input, bool $expected): void
    {
        $stopSpammer = new StopSpammer();
        $this->assertSame($expected, $stopSpammer->badIP($input));
    }

    public static function provideBadIpData(): Generator
    {
        yield 'invalid ip' => [
            'input' => 'not_an_ip',
            'expected' => true
        ];
        yield 'ipv6' => [
            'input' => '2001:0db8:85a3:0000:0000:8a2e:0370:7334',
            'expected' => false
        ];
    }
}
