<?php

use League\Event\EventDispatcher;
use PHPUnit\Framework\TestCase;
use Imponeer\DataFilter\DataFilter;
use Imponeer\DataFilter\StopSpammer;
use Imponeer\DataFilter\Configuration\MainConfig;
use Imponeer\DataFilter\Configuration\MultilangConfig;
use Imponeer\DataFilter\Configuration\UserConfig;
use Imponeer\DataFilter\Configuration\PersonaConfig;
use Imponeer\DataFilter\Configuration\CensorConfig;
use Imponeer\DataFilter\Configuration\PluginsConfig;
use Imponeer\DataFilter\Configuration\SmilesConfig;

require_once __DIR__ . '/TestHTMLFilter.php';

class DataFilterTest extends TestCase
{
    private DataFilter $filter;

    protected function setUp(): void
    {
        $this->filter = new DataFilter(
            new StopSpammer(),
            new EventDispatcher(),
            new TestHTMLFilter(),
            new MainConfig(),
            new MultilangConfig(),
            new UserConfig(),
            new PersonaConfig(),
            new CensorConfig(),
            new PluginsConfig(),
            new SmilesConfig('/tmp', [])
        );
    }

    public function testCheckUrlString()
    {
        $this->assertTrue($this->filter->checkUrlString('https://example.com'));
        $this->assertFalse($this->filter->checkUrlString("javascript:alert('xss')"));
        $this->assertFalse($this->filter->checkUrlString("\x01bad"));
    }

    public function testNl2Br()
    {
        $this->assertEquals('a<br />b', $this->filter->nl2Br("a\nb"));
        $this->assertEquals('a<br />b', $this->filter->nl2Br("a\rb"));
        $this->assertEquals('a<br />b', $this->filter->nl2Br("a\r\nb"));
    }

    public function testHtmlSpecialChars()
    {
        $this->assertEquals('&lt;', $this->filter->htmlSpecialChars('<'));
        $this->assertEquals('&amp;', $this->filter->htmlSpecialChars('&amp;'));
        $this->assertEquals('&amp;nbsp;', $this->filter->htmlSpecialChars('&nbsp;'));
    }

    public function testUndoHtmlSpecialChars()
    {
        $this->assertEquals('"', $this->filter->undoHtmlSpecialChars('&quot;'));
        $this->assertEquals('&apos;', $this->filter->undoHtmlSpecialChars('&apos;'));
    }

    public function testHtmlEntities()
    {
        $this->assertEquals('&lt;', $this->filter->htmlEntities('<'));
        $this->assertEquals('&amp;', $this->filter->htmlEntities('&amp;'));
        $this->assertEquals('&amp;nbsp;', $this->filter->htmlEntities('&nbsp;'));
    }

    public function testAddSlashes()
    {
        $this->assertEquals('a\\\\b', $this->filter->addSlashes('a\b', '\\'));
        $this->assertEquals('a\nb', $this->filter->addSlashes('a\nb', ''));
    }

    public function testCleanArray()
    {
        $input = [
            'a' => '',
            'b' => 0,
            'c' => 'foo',
            'd' => [
                'e' => '',
                'f' => 'bar',
                'g' => []
            ],
        ];
        $expected = [
            'b' => 0,
            'c' => 'foo',
            'd' => [
                'f' => 'bar',
            ],
        ];
        $this->assertEquals($expected, $this->filter->cleanArray($input));
    }
}
