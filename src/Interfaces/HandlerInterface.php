<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\Interfaces;

use Imponeer\DataFilter\DataFilter;

/**
 * Interface for all CheckVar handlers
 *
 * All handlers must implement this interface to ensure consistent behavior
 * when processing options for data validation and filtering.
 */
interface HandlerInterface
{
    /**
     * Process and validate options for the handler
     *
     * This method modifies the passed options by reference to ensure
     * they are valid for the specific handler type.
     *
     * @param mixed $options1 First option parameter (modified by reference)
     * @param mixed $options2 Second option parameter (modified by reference)
     * @return void
     */
    public function autoconfigure(mixed &$options1, mixed &$options2): void;

    /**
     * Check and validate data using the handler's specific logic
     *
     * @param mixed $data Data to be checked and validated
     * @param mixed $options1 First option parameter
     * @param mixed $options2 Second option parameter
     * @return mixed Validated/processed data or false on failure
     */
    public function check(mixed $data, mixed $options1, mixed $options2): mixed;
}
