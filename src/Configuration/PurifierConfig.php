<?php

declare(strict_types=1);

namespace Imponeer\DataFilter\Configuration;

readonly class PurifierConfig
{

    public function __construct(
        public string $htmlDefinitionId, // purifier_HTML_DefinitionID
        public int $htmlDefinitionRev, // purifier_HTML_DefinitionRev
        public string $htmlDoctype, // purifier_HTML_Doctype
        public string $htmlAllowedElements, // purifier_HTML_AllowedElements
        public string $htmlAllowedAttributes, // purifier_HTML_AllowedAttributes
        public string $htmlForbiddenElements, // purifier_HTML_ForbiddenElements
        public string $htmlForbiddenAttributes, // purifier_HTML_ForbiddenAttributes
        public int $htmlMaxImgLength, // purifier_HTML_MaxImgLength
        public string $htmlTidyLevel, // purifier_HTML_TidyLevel
        public bool $htmlSafeEmbed, // purifier_HTML_SafeEmbed
        public bool $htmlSafeObject, // purifier_HTML_SafeObject
        public bool $htmlSafeIframe, // purifier_HTML_SafeIframe
        public bool $htmlAttrNameUseCDATA, // purifier_HTML_AttrNameUseCDATA
        public bool $htmlFlashAllowFullScreen, // purifier_HTML_FlashAllowFullScreen
        public bool $outputFlashCompat, // purifier_Output_FlashCompat
        public int $cssDefinitionRev, // purifier_CSS_DefinitionRev
        public bool $cssAllowImportant, // purifier_CSS_AllowImportant
        public bool $cssAllowTricky, // purifier_CSS_AllowTricky
        public string $cssAllowedProperties, // purifier_CSS_AllowedProperties
        public int $cssMaxImgLength, // purifier_CSS_MaxImgLength
        public bool $cssProprietary, // purifier_CSS_Proprietary
        public bool $autoFormatAutoParagraph, // purifier_AutoFormat_AutoParagraph
        public bool $autoFormatDisplayLinkURI, // purifier_AutoFormat_DisplayLinkURI
        public bool $autoFormatLinkify, // purifier_AutoFormat_Linkify
        public bool $autoFormatPurifierLinkify, // purifier_AutoFormat_PurifierLinkify
        public array $autoFormatCustom, // purifier_AutoFormat_Custom
        public bool $autoFormatRemoveEmpty, // purifier_AutoFormat_RemoveEmpty
        public bool $autoFormatRemoveEmptyNbsp, // purifier_AutoFormat_RemoveEmptyNbsp
        public array $autoFormatRemoveEmptyNbspExceptions, // purifier_AutoFormat_RemoveEmptyNbspExceptions
        public bool $coreEscapeNonASCIICharacters, // purifier_Core_EscapeNonASCIICharacters
        public array $coreHiddenElements, // purifier_Core_HiddenElements
        public bool $coreNormalizeNewlines, // purifier_Core_NormalizeNewlines
        public bool $coreRemoveInvalidImg, // purifier_Core_RemoveInvalidImg
        public string $coreEncoding, // charset
        public string $cacheSerializerPath, // cache_path
        public string $uriHost, // purifier_URI_Host
        public string $uriBase, // purifier_URI_Base
        public bool $uriDisable, // purifier_URI_Disable
        public bool $uriDisableExternal, // purifier_URI_DisableExternal
        public bool $uriDisableExternalResources, // purifier_URI_DisableExternalResources
        public bool $uriDisableResources, // purifier_URI_DisableResources
        public bool $uriMakeAbsolute, // purifier_URI_MakeAbsolute
        public array $uriHostBlacklist, // purifier_URI_HostBlacklist
        public array $uriAllowedSchemes, // purifier_URI_AllowedSchemes
        public string $uriDefinitionId, // purifier_URI_DefinitionID
        public int $uriDefinitionRev, // purifier_URI_DefinitionRev
        public string $uriSafeIframeRegexp, // purifier_URI_SafeIframeRegexp
        public array $attrAllowedFrameTargets, // purifier_Attr_AllowedFrameTargets
        public array $attrAllowedRel, // purifier_Attr_AllowedRel
        public string $attrAllowedClasses, // purifier_Attr_AllowedClasses
        public string $attrForbiddenClasses, // purifier_Attr_ForbiddenClasses
        public string $attrDefaultInvalidImage, // purifier_Attr_DefaultInvalidImage
        public string $attrDefaultInvalidImageAlt, // purifier_Attr_DefaultInvalidImageAlt
        public string $attrDefaultImageAlt, // purifier_Attr_DefaultImageAlt
        public bool $attrClassUseCDATA, // purifier_Attr_ClassUseCDATA
        public string $attrIdPrefix, // purifier_Attr_IDPrefix
        public bool $attrEnableId, // purifier_Attr_EnableID
        public string $attrIdPrefixLocal, // purifier_Attr_IDPrefixLocal
        public array $attrIdBlacklist, // purifier_Attr_IDBlacklist
        public bool $filterExtractStyleBlocksEscaping, // purifier_Filter_ExtractStyleBlocks_Escaping
        public string $filterExtractStyleBlocksScope, // purifier_Filter_ExtractStyleBlocks_Scope
        public bool $filterYouTube, // purifier_Filter_YouTube
        public bool $filterAllowCustom = false, // purifier_Filter_AllowCustom
        public bool $enable = true, // enable_purifier
        public bool $filterExtractStyleBlocks = false, // purifier_Filter_ExtractStyleBlocks
    )
    {
    }

}