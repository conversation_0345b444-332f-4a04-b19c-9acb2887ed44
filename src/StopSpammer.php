<?php

namespace Imponeer\DataFilter;

use GuzzleHttp\Client;
use Imponeer\DataFilter\Exceptions\ServerConnectionException;
use Throwable;

class StopSpammer {

    /**
     * Constructor
     */
    public function __construct(
        private ?Client $client = null,
        private readonly string $apiUrl = "https://www.stopforumspam.com/api?",
    ) {
        if (!$this->client) {
            $this->client = new Client([
                'timeout'  => 5.0,
            ]);
        }
    }

    /**
     * Check the StopForumSpam API for a specific field (username, email or IP)
     *
     * @param string $field field to check
     * @param string $value value to validate
     * @return true if spammer was found with passed info
     */
    public function checkForField(string $field, string $value): bool {
        $spam = false;
        $url = $this->apiUrl . $field . '=' . urlencode($value);
        try {
            $response = $this->client->request('GET', $url);
            $output = (string) $response->getBody();
            if (preg_match("#<appears>(.*)</appears>#i", $output, $out)) {
                $spam = $out[1];
            }
        } catch (Throwable $e) {
            throw new ServerConnectionException($url, 0, $e);
        }
        return $spam === 'yes';
    }

    /**
     * Check the StopForumSpam API for specified username
     *
     * @param string $username username to check
     * @return true if spammer was found with this username
     */
    public function badUsername(string $username): bool {
        return $this->checkForField('username', $username);
    }

    /**
     * Check the StopForumSpam API for specified email
     *
     * @param string $email email to check
     * @return true if spammer was found with this email
     */
    public function badEmail(string $email): bool {
        return $this->checkForField('email', $email);
    }

    /**
     * Check the StopForumSpam API for specified IP
     *
     * @param string $ip ip to check
     * @return true if spammer was found with this IP
     */
    public function badIP(string $ip): bool {
        // return TRUE if it's not a valid IP
        if (!filter_var($ip, FILTER_VALIDATE_IP)) {
            return TRUE;
        }
        // return FALSE if it is a valid IPv6 address - only until IPv6 can be checked without error
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            return FALSE;
        }
        return $this->checkForField('ip', $ip);
    }
}