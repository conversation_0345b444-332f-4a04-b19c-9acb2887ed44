<?php

namespace Imponeer\DataFilter;

use Imponeer\DataFilter\Exceptions\HandlerNotFoundException;
use Imponeer\DataFilter\Interfaces\HandlerInterface;
use Imponeer\DataFilter\Configuration\CensorConfig;
use Imponeer\DataFilter\Configuration\MainConfig;
use Imponeer\DataFilter\Configuration\MultilangConfig;
use Imponeer\DataFilter\Configuration\PersonaConfig;
use Imponeer\DataFilter\Configuration\PluginsConfig;
use Imponeer\DataFilter\Configuration\UserConfig;
use Imponeer\DataFilter\Events\AfterHtmlDisplayEvent;
use Imponeer\DataFilter\Events\AfterHtmlInputEvent;
use Imponeer\DataFilter\Events\AfterTextareaDisplayEvent;
use Imponeer\DataFilter\Events\AfterTextareaInputEvent;
use Imponeer\DataFilter\Events\BeforeTextareaInputEvent;
use Imponeer\DataFilter\Events\BeforeTextareaDisplayEvent;
use Imponeer\DataFilter\Events\BeforeHtmlInputEvent;
use Imponeer\DataFilter\Events\BeforeHtmlDisplayEvent;
use Imponeer\DataFilter\Managers\CheckVarHandlerManager;
use Imponeer\DataFilter\Managers\SmilesManager;
use Psr\EventDispatcher\EventDispatcherInterface;

class DataFilter {

    public function __construct(
        private readonly EventDispatcherInterface $eventDispatcher,
        private readonly HTMLFilter $htmlFilter,
        private readonly MainConfig $mainConfig,
        private readonly MultilangConfig $multilangConfig,
        private readonly PersonaConfig $personaConfig,
        private readonly CensorConfig $censorConfig, // $icmsConfigCensor = icms::$config->getConfigsByCat(ICMS_CONF_CENSOR);
        private readonly PluginsConfig $pluginsConfig,
        private readonly SmilesManager $smilesManager,
        private readonly CheckVarHandlerManager $checkVarHandlerManager,
        private readonly string $charset = 'UTF-8', // _CHARSET
        private readonly bool $useMultiByte = false, // XOOPS_USE_MULTIBYTES
        private readonly string $baseUrl = 'http://localhost/', // ICMS_URL
        private readonly string $quoteText = 'Quote:', // _QUOTE
    ) {
    }

    // -------- Public Functions --------

    /**
     *
     * <AUTHOR> montgomery (<EMAIL>)
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param $text
     * @param $msg
     */
    public function filterDebugInfo($text, $msg) {
        echo "<div style='padding: 5px; color: red; font-weight: bold'>$text</div>";
        echo "<div><pre>";
        print_r($msg);
        echo "</pre></div>";
    }

    /**
     * Filters out invalid strings included in URL, if any
     *
     * <AUTHOR> montgomery (<EMAIL>)
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param array $matches
     * @return string
     */
    public function _filterImgUrl($matches) {
        if ($this->checkUrlString($matches[2])) {
            return $matches[0];
        }

        return '';
    }

    /**
     * Checks if invalid strings are included in URL
     *
     * <AUTHOR> montgomery (<EMAIL>)
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     */
    public function checkUrlString(string $text): bool {
        // Check control code
        if (preg_match("/[\0-\31]/", $text)) {
            return false;
        }
        // check black pattern(deprecated)
        return !preg_match("/^(javascript|vbscript|about):/i", $text);
    }

    /**
     * Convert linebreaks to <br /> tags
     */
    public function nl2Br(string $text): string {
        return preg_replace(
            "/(\015\012)|(\015)|(\012)/",
            "<br />",
            $text
        );
    }

    /**
     * for displaying data in html textbox forms
     *
     * @param string $text
     * @return string
     */
    public function htmlSpecialChars(string $text): string {
        return preg_replace(
            array(
                "/&amp;/i",
                "/&nbsp;/i"
            ),
            array(
                '&',
                '&amp;nbsp;'
            ),
            @htmlspecialchars($text, ENT_QUOTES, $this->charset)
        );
    }

    /**
     * Reverses {@link htmlSpecialChars()}
     *
     * @param string $text
     * @return string
     */
    public function undoHtmlSpecialChars(string $text): string {
        return htmlspecialchars_decode($text, ENT_QUOTES);
    }

    public function htmlEntities(string $text): string {
        return preg_replace(
            array(
                "/&amp;/i",
                "/&nbsp;/i"
            ),
            array(
                '&',
                '&amp;nbsp;'
            ),
            @htmlentities($text, ENT_QUOTES, $this->charset)
        );
    }

    /**
     *
     * @param string $text the text to apply the slashes to
     *        string $param which characters to apply the escaping to.
     * @return string Add slashes to the text if magic_quotes_gpc is turned off (and that should be always on >= PHP 5.4!!!).
     */
    public function addSlashes(string $text, ?string $param = null): string
    {
        return addcslashes($text, $param);
    }

    /**
     * Note: magic_quotes_gpc and magic_quotes_runtime are deprecated as of PHP5.3.0
     * (and that should be always on >= PHP 5.4!!!)
     *
     * @param string $text
     * @return string
     * @deprecated we shouldn't be using this as a 'filter'
     */
    public function stripSlashesGPC($text) {
        return $text;
    }

    /**
     * Filters Multidimensional Array Recursively removing keys with empty values
     *
     * <AUTHOR> montgomery (<EMAIL>)
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param array $arr Array to be filtered
     * @return array $array
     */
    public function cleanArray(array $arr): array {
        $rtn = array();

        foreach ($arr as $key => $a) {
            if (!is_array($a) && (!empty($a) || $a === 0)) {
                $rtn[$key] = $a;
            } elseif (is_array($a)) {
                if (count($a) > 0) {
                    $a = $this->cleanArray($a);
                    $rtn[$key] = $a;
                    if (count($a) === 0) {
                        unset($rtn[$key]);
                    }
                }
            }
        }
        return $rtn;
    }

    /*
     * Public Function checks Variables using specified filter type
     *
     * @TODO needs error trapping for debug if invalid types and options used!!
     *
     * <AUTHOR> montgomery (<EMAIL>)
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param string $data Data to be checked
     * @param string $type Type of Filter To use for Validation
     * Valid Filter Types:
     * 'url' = Checks & validates URL
     * 'email' = Checks & validates Email Addresses
     * 'ip' = Checks & validates IP Addresses
     * 'str' = Checks & Sanitizes String Values
     * 'int' = Validates Integer Values
     * 'html' = Validates HTML
     * 'text' = Validates plain textareas (Non HTML)
     * 'special' htmlspecialchars filter options
     *
     * @param mixed $options1 Options to use with specified filter
     * Valid Filter Options:
     * URL:
     * 'scheme' = URL must be an RFC compliant URL (like http://example)
     * 'host' = URL must include host name (like http://www.example.com)
     * 'path' = URL must have a path after the domain name (like www.example.com/example1/)
     * 'query' = URL must have a query string (like "example.php?name=Vaughan&age=34")
     * EMAIL:
     * 'true' = Generate an email address that is protected from spammers
     * 'false' = Generate an email address that is NOT protected from spammers
     * IP:
     * 'ipv4' = Requires the value to be a valid IPv4 IP (like ***************)
     * 'ipv6' = Requires the value to be a valid IPv6 IP (like 2001:0db8:85a3:08d3:1319:8a2e:0370:7334)
     * 'rfc' = Requires the value to be a RFC specified private range IP (like ***********)
     * 'res' = Requires that the value is not within the reserved IP range. both IPV4 and IPV6 values
     * STR:
     * 'noencode' = Do NOT encode quotes
     * 'striplow' = Strip characters with ASCII value below 32
     * 'striphigh' = Strip characters with ASCII value above 127
     * 'encodelow' = Encode characters with ASCII value below 32
     * 'encodehigh' = Encode characters with ASCII value above 127
     * 'encodeamp' = Encode the & character to &amp;
     * SPECIAL:
     * 'striplow' = Strip characters with ASCII value below 32
     * 'striphigh' = Strip characters with ASCII value above 32
     * 'encodehigh' = Encode characters with ASCII value above 32
     * INT:
     * minimum integer range value
     * HTML:
     * 'input' = Filters HTML for input to DB
     * 'output' = Filters HTML for rendering output
     * 'print' = Filters HTML for output to Printer
     * 'edit' = used for edit content forms
     * TEXT:
     * 'input' = Filters plain text for input to DB
     * 'output' = Filters plain text for rendering output
     * 'print' = Filters plain text for output to printer
     *
     * @param mixed $options2 Options to use with specified filter options1
     * URL:
     * 'true' = URLEncode the URL (ie. http://www.example > http%3A%2F%2Fwww.example)
     * 'false' = Do Not URLEncode the URL
     * EMAIL:
     * 'true' = Reject if email is banned (Uses: $icmsConfigUser['bad_emails'])
     * 'false' = Do Not use Email Blacklist
     * IP:
     * NOT USED!
     * INT:
     * maximum integer range value
     *
     * @return mixed
     *
     * <AUTHOR> montgomery (<EMAIL>)
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param $data
     * @param $type
     * @param $options1
     * @param $options2
     */
    public function checkVar($data, $type, $options1 = '', $options2 = '') {
        $valid_types = array('url', 'email', 'ip', 'str', 'int', 'special', 'html', 'text');

        if (!in_array($type, $valid_types, true)) {
            return false;
        }

        try {
            $handler = $this->checkVarHandlerManager->get($type);
            $handler->autoconfigure($options1, $options2);
            
            return $handler->check($data, $options1, $options2);
        } catch (HandlerNotFoundException $exception) {
            return false;
        }
    }

    /**
     * Filter an array of variables, such as $_GET or $_POST, using a set of filters.
     *
     * Any items in the input array not found in the filter array will be filtered as
     * a string.
     *
     * <AUTHOR> montgomery (<EMAIL>)
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param array $input items to be filtered
     * @param array $filters the keys of this array should match the keys in
     *        the input array and the values should be valid types
     *        for the checkVar method
     * @param bool $strict when true (default), items not in the filter array will be discarded
     *        when false, items not in the filter array will be filtered as strings and included
     * @return array
     */
    public function checkVarArray(array $input, array $filters, bool $strict = true): array
    {
        foreach (array_intersect_key($input, $filters) as $key => $value) {
            $options[0] = $options[1] = '';
            if (array_key_exists('options', $filters) && isset($filters[$key]['options'][0])) {
                $options[0] = $filters[$key]['options'][0];
            }
            if (array_key_exists('options', $filters) && isset($filters[$key]['options'][1])) {
                $options[1] = $filters[$key]['options'][1];
            }
            if (is_array($filters[$key])) {
                $filter = $filters[$key][0];
            } else {
                $filter = $filters[$key];
            }
            if (is_array($input[$key])) {
                // pass the filter type into the array and all members in it
                $inner_filter = array_fill_keys(array_keys($input[$key]), $filter);
                $output[$key] = $this->checkVarArray($input[$key], $inner_filter, true);
            } else {
                $output[$key] = $this->checkVar($input[$key], $filter, $options[0], $options[1]);
            }
        }

        if (!$strict) {
            foreach ($diff = array_diff_key($input, $filters) as $key => $value) {
                if (is_array($diff[$key])) {
                    $output[$key] = $this->checkVarArray($diff[$key], array($key => 'str'), false);
                } else {
                    $output[$key] = $this->checkVar($diff[$key], 'str');
                }
            }
        }
        return $output;
    }

    /**
     * Filters textarea form data for INPUt to DB (text only!!)
     * For HTML please use icms_core_HTMLFilter::filterHTMLinput()
     *
     * <AUTHOR> montgomery (<EMAIL>)
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param string $text
     * @return string
     */
    public function filterTextareaInput(string $text): string {
        $event = $this->eventDispatcher->dispatch(new BeforeTextareaInputEvent($text));
        assert($event instanceof BeforeTextareaInputEvent);

        $text = $this->htmlSpecialChars(strip_tags($event->content));

        $event = $this->eventDispatcher->dispatch(new AfterTextareaInputEvent($text));
        assert($event instanceof AfterTextareaInputEvent);

        return $event->content;
    }

    /**
     * Filters textarea for DISPLAY purposes (text only!!)
     * For HTML please use icms_core_HTMLFilter::filterHTMLdisplay()
     *
     * @param string $text
     * @param bool $smiley allow smileys?
     * @param bool $icode allow icmscode?
     * @param bool $image allow inline images?
     * @param bool $br convert linebreaks?
     * @return string
     */
    public function filterTextareaDisplay(string $text, bool $smiley = true, bool $icode = true, bool $image = true, bool $br = true): string {
        if (!$text) {
            return '';
        }

        $event = $this->eventDispatcher->dispatch(
            new BeforeTextareaDisplayEvent($text, $smiley, $icode, $image, $br)
        );
        assert($event instanceof BeforeTextareaDisplayEvent);

        $text = $event->content;
        $smiley = $event->smiley;
        $icode = $event->icode;
        $image = $event->image;
        $br = $event->br;

        $text = str_replace(
            array(
                '<!-- input filtered -->',
                '<!-- filtered with htmlpurifier -->'
            ),
            '',
            $text
        );

        $text = $this->htmlSpecialChars($text);
        $text = $this->codePreConv($text, $icode);
        $text = $this->makeClickable($text);
        if ($smiley) {
            $text = $this->smilesManager->replaceSmileys($text);
        }
        if ($icode) {
            if ($image) {
                $text = $this->codeDecode($text);
            } else {
                $text = $this->codeDecode($text, 0);
            }
        }
        if ($br) {
            $text = $this->nl2Br($text);
        }
        $text = $this->codeConv($text, $icode, $image);

        $event = $this->eventDispatcher->dispatch(
            new AfterTextareaDisplayEvent($text, $smiley, $icode, $image, $br)
        );
        assert($event instanceof AfterTextareaDisplayEvent);

        return $event->content;
    }

    /**
     * Filters HTML form data for INPUT to DB
     *
     * <AUTHOR> montgomery (<EMAIL>)
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param string $html
     * @param bool $smiley allow smileys?
     * @param bool $icode allow icmscode?
     * @param bool $image allow inline images?
     * @return string
     */
    public function filterHTMLinput(string $html, bool $smiley = true, bool $icode = true, bool $image = true, bool $br = false): string
    {
        $event = $this->eventDispatcher->dispatch(
            new BeforeHtmlInputEvent($html, $smiley, $icode, $image, $br)
        );
        assert($event instanceof BeforeHtmlInputEvent);

        $html = $event->content;
        $smiley = $event->smiley;
        $icode = $event->icode;
        $image = $event->image;
        $br = $event->br;

        $html = str_replace('<!-- input filtered -->', '', $html);

        $html = $this->codePreConv($html, 1);
        $html = $this->smilesManager->replaceSmileys($html);
        $html = $this->codeDecode($html);
        $html = $this->codeConv($html, 1, 1);

        $html = $this->htmlFilter->filterHTML($html);

        $purified = strpos($html, '<!-- filtered with htmlpurifier -->');
        if ($purified === false && $br) {
            $html = $this->nl2Br($html);
        }

        $html .= '<!-- input filtered -->';

        $event = $this->eventDispatcher->dispatch(
            new AfterHtmlInputEvent($html, $smiley, $icode, $image, $br)
        );
        assert($event instanceof AfterHtmlInputEvent);

        return $event->content;
    }

    /**
     * Filters HTML form data for Display Only
     * we don't really require the icmscode stuff, but we need to for content already in the DB before
     * we start filtering on INPUT instead of OUTPUT!!
     *
     * <AUTHOR> montgomery (<EMAIL>)
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param string $html
     * @param bool $icode allow icmscode?
     * @return string
     */
    public function filterHTMLdisplay(string $html, bool $icode = true, bool $br = false): string {
        $event = $this->eventDispatcher->dispatch(
            new BeforeHtmlDisplayEvent($html, $icode, $br)
        );
        assert($event instanceof BeforeHtmlDisplayEvent);

        $html = $event->content;
        $icode = $event->icode;
        $br = $event->br;

        $ifiltered = strpos($html, '<!-- input filtered -->');
        if ($ifiltered === false) {
            $html = $this->codePreConv($html, true);
            $html = $this->smilesManager->replaceSmileys($html);
            $html = $this->codeDecode($html);
            $html = $this->codeConv($html, true, true);

            $html = $this->htmlFilter->filterHTML($html);

            // $html .= '<!-- warning! output filtered only -->';

            $purified = strpos($html, '<!-- filtered with htmlpurifier -->');
            if ($purified === false || $br) {
                $html = $this->nl2Br($html);
            }
        }

        if ($this->mainConfig->debugMode !== 0) {
            $purified = strpos($html, '<!-- filtered with htmlpurifier -->');
            if ($ifiltered !== false) {
                $html = str_replace('<!-- input filtered -->', '', $html);
            }

            if ($purified !== false) {
                $html = str_replace('<!-- filtered with htmlpurifier -->', '', $html);
                // Debug logging removed - can be implemented via event listeners
            }
        } else {
            $html = str_replace(
                array(
                    '<!-- input filtered -->',
                    '<!-- filtered with htmlpurifier -->'
                ),
                '',
                $html
            );
        }

        $html = $this->makeClickable($html);
        $html = $this->censorString($html);

        $event = $this->eventDispatcher->dispatch(new AfterHtmlDisplayEvent($html, $icode, $br));
        assert($event instanceof AfterHtmlDisplayEvent);

        return $event->content;
    }

    /**
     * Replace icmsCodes with their equivalent HTML formatting
     *
     * @param string $text
     * @param bool $allowimage Allow images in the text?
     *        On false, uses links to images.
     * @return string
     */
    public function codeDecode(string $text, bool $allowimage = false): string {
        $patterns = array();
        $replacements = array();
        $patterns[] = "/\[siteurl=(['\"]?)([^\"'<>]*)\\1](.*)\[\/siteurl\]/sU";
        $replacements[] = '<a href="' . $this->baseUrl . '/\\2">\\3</a>';
        $patterns[] = "/\[url=(['\"]?)(http[s]?:\/\/[^\"'<>]*)\\1](.*)\[\/url\]/sU";
        $replacements[] = '<a href="\\2" rel="external">\\3</a>';
        $patterns[] = "/\[url=(['\"]?)(ftp?:\/\/[^\"'<>]*)\\1](.*)\[\/url\]/sU";
        $replacements[] = '<a href="\\2" rel="external">\\3</a>';
        $patterns[] = "/\[url=(['\"]?)([^\"'<>]*)\\1](.*)\[\/url\]/sU";
        $replacements[] = '<a href="http://\\2" rel="external">\\3</a>';
        $patterns[] = "/\[color=(['\"]?)([a-zA-Z0-9]*)\\1](.*)\[\/color\]/sU";
        $replacements[] = '<span style="color: #\\2;">\\3</span>';
        $patterns[] = "/\[size=(['\"]?)([a-z0-9-]*)\\1](.*)\[\/size\]/sU";
        $replacements[] = '<span style="font-size: \\2;">\\3</span>';
        $patterns[] = "/\[font=(['\"]?)([^;<>\*\(\)\"']*)\\1](.*)\[\/font\]/sU";
        $replacements[] = '<span style="font-family: \\2;">\\3</span>';
        $patterns[] = "/\[email]([^;<>\*\(\)\"']*)\[\/email\]/sU";
        $replacements[] = '<a href="mailto:\\1">\\1</a>';
        $patterns[] = "/\[b](.*)\[\/b\]/sU";
        $replacements[] = '<strong>\\1</strong>';
        $patterns[] = "/\[i](.*)\[\/i\]/sU";
        $replacements[] = '<em>\\1</em>';
        $patterns[] = "/\[u](.*)\[\/u\]/sU";
        $replacements[] = '<u>\\1</u>';
        $patterns[] = "/\[d](.*)\[\/d\]/sU";
        $replacements[] = '<del>\\1</del>';
        $patterns[] = "/\[center](.*)\[\/center\]/sU";
        $replacements[] = '<div align="center">\\1</div>';
        $patterns[] = "/\[left](.*)\[\/left\]/sU";
        $replacements[] = '<div align="left">\\1</div>';
        $patterns[] = "/\[right](.*)\[\/right\]/sU";
        $replacements[] = '<div align="right">\\1</div>';
        $patterns[] = "/\[img align=center](.*)\[\/img\]/sU";
        if (!$allowimage) {
            $replacements[] = '<div style="margin: 0 auto; text-align: center;"><a href="\\1" rel="external">\\1</a></div>';
        } else {
            $replacements[] = '<div style="margin: 0 auto; text-align: center;"><img src="\\1" alt="" /></div>';
        }
        $patterns[] = "/\[img align=(['\"]?)(left|right)\\1]([^\"\(\)\?\&'<>]*)\[\/img\]/sU";
        $patterns[] = "/\[img]([^\"\(\)\?\&'<>]*)\[\/img\]/sU";
        $patterns[] = "/\[img align=(['\"]?)(left|right)\\1 id=(['\"]?)([0-9]*)\\3]([^\"\(\)\?\&'<>]*)\[\/img\]/sU";
        $patterns[] = "/\[img id=(['\"]?)([0-9]*)\\1]([^\"\(\)\?\&'<>]*)\[\/img\]/sU";
        if (!$allowimage) {
            $replacements[] = '<a href="\\3" rel="external">\\3</a>';
            $replacements[] = '<a href="\\1" rel="external">\\1</a>';
            $replacements[] = '<a href="' . $this->baseUrl . '/image.php?id=\\4" rel="external">\\5</a>';
            $replacements[] = '<a href="' . $this->baseUrl . '/image.php?id=\\2" rel="external">\\3</a>';
        } else {
            $replacements[] = '<img src="\\3" align="\\2" alt="" />';
            $replacements[] = '<img src="\\1" alt="" />';
            $replacements[] = '<img src="' . $this->baseUrl . '/image.php?id=\\4" align="\\2" alt="\\5" />';
            $replacements[] = '<img src="' . $this->baseUrl . '/image.php?id=\\2" alt="\\3" />';
        }
        $patterns[] = "/\[quote]/sU";
        $replacements[] = $this->quoteText . '<div class="icmsQuote"><blockquote><p>';
        $patterns[] = "/\[\/quote]/sU";
        $replacements[] = '</p></blockquote></div>';
        $text = str_replace("\x00", "", $text);
        $c = "[\x01-\x1f]*";
        $patterns[] = "/j{$c}a{$c}v{$c}a{$c}s{$c}c{$c}r{$c}i{$c}p{$c}t{$c}:/si";
        $replacements[] = "(script removed)";
        $patterns[] = "/a{$c}b{$c}o{$c}u{$c}t{$c}:/si";
        $replacements[] = "about :";
        $text = preg_replace($patterns, $replacements, $text);
        assert(is_string($text));
        $text = $this->codeDecode_extended($text);
        assert(is_string($text));
        return $text;
    }

    /**
     * Make links in the text clickable
     *
     * @param string $text
     * @return string
     */
    public function makeClickable(string $text): string {
        $text = ' ' . $text;
        $patterns = array("/(^|[^]_a-z0-9-=\"'\/])([a-z]+?):\/\/([^, \r\n\"\(\)'<>]+)/i", "/(^|[^]_a-z0-9-=\"'\/])www\.([a-z0-9\-]+)\.([^, \r\n\"\(\)'<>]+)/i", "/(^|[^]_a-z0-9-=\"'\/])ftp\.([a-z0-9\-]+)\.([^,\r\n\"\(\)'<>]+)/i" /* , "/(^|[^]_a-z0-9-=\"'\/:\.])([a-z0-9\-_\.]+?)@([^, \r\n\"\(\)'<>\[\]]+)/i" */
        );
        $replacements = array("\\1<a href=\"\\2://\\3\" rel=\"external\">\\2://\\3</a>", "\\1<a href=\"http://www.\\2.\\3\" rel=\"external\">www.\\2.\\3</a>", "\\1<a href=\"ftp://ftp.\\2.\\3\" rel=\"external\">ftp.\\2.\\3</a>" /* , "\\1<a href=\"mailto:\\2@\\3\">\\2@\\3</a>" */
        );
        $text = preg_replace($patterns, $replacements, $text);
        if ($this->personaConfig->shortenUrl) {
            $links = explode('<a', $text);
            $countlinks = count($links);
            for ($i = 0; $i < $countlinks; $i++ ) {
                $link = $links[$i];
                $link = (preg_match('#(.*)(href=")#is', $link)) ? '<a' . $link : $link;
                $begin = strpos($link, '>') + 1;
                $end = strpos($link, '<', $begin);
                $length = $end - $begin;
                $urlname = substr($link, $begin, $length);

                $maxlength = $this->personaConfig->maxUrlLong;
                $cutlength = $this->personaConfig->preCharsLeft;
                $endlength = -$this->personaConfig->lastCharsLeft;
                $middleurl = " ... ";
                $chunked = (strlen($urlname) > $maxlength && preg_match('#^(https://|http://|ftp://|www\.)#is', $urlname)) ? substr_replace($urlname, $middleurl, $cutlength, $endlength) : $urlname;
                $text = str_replace('>' . $urlname . '<', '>' . $chunked . '<', $text);
            }
        }
        return substr($text, 1);
    }

    public function __call(string $name, array $arguments): mixed
    {
        return match ($name) {
            'smiley' => call_user_func_array([$this->smilesManager, 'replaceSmileys'], $arguments),
            default => throw new \BadMethodCallException("Method $name does not exist"),
        };
    }

    /**
     * Get the charset used by this DataFilter instance
     *
     * @return string
     */
    public function getCharset(): string {
        return $this->charset;
    }

    /**
     * Replaces banned words in a string with their replacements
     *
     * @param string $text
     * @return string
     *
     */
    public function censorString(string $text): string {
        if ($this->censorConfig->enable && !empty($this->censorConfig->words)) {
            $replacement = $this->censorConfig->replace;
            foreach ($this->censorConfig->words as $bad) {
                if (!empty($bad)) {
                    $bad = quotemeta($bad);
                    $patterns[] = "/(\s)" . $bad . "/siU";
                    $replacements[] = "\\1" . $replacement;
                    $patterns[] = "/^" . $bad . "/siU";
                    $replacements[] = $replacement;
                    $patterns[] = "/(\n)" . $bad . "/siU";
                    $replacements[] = "\\1" . $replacement;
                    $patterns[] = "/]" . $bad . "/siU";
                    $replacements[] = "]" . $replacement;
                    $text = preg_replace($patterns, $replacements, $text);
                }
            }
        }
        return $text;
    }

    /**
     * Sanitizing of [code] tag
     */
    public function codePreConv(string $text, bool $imcode): string {
        if ($imcode) {
            $patterns = "/\[code](.*)\[\/code\]/sU";
            $text = preg_replace_callback($patterns, function ($match) {
                return '[code]' . base64_encode($match[1]) . '[/code]';
            }, $text);
        }
        return $text;
    }

    /**
     * Converts text to imcode
     *
     * @param string $text Text to convert
     * @param bool $imcode Is the code Xcode?
     * @param bool $image configuration for the purifier
     * @return string $text the converted text
     */
    public function codeConv(string $text, bool $imcode = true, bool $image = true): string {
        if ($imcode) {
            $patterns = "/\[code](.*)\[\/code\]/sU";
            $text = preg_replace_callback($patterns, function ($matches) use ($image) {
                $code = $this->codeSanitizer($matches[1], $image);
                return '<div class="icmsCode">' . $code . '</div>';
            }, $text);
        }
        return $text;
    }

    /**
     * Sanitizes decoded string
     *
     * @param string $str String to sanitize
     * @param bool $image Is the string an image
     * @return string $str The sanitized decoded string
     */
    public function codeSanitizer(string $str, bool $image = true): string {
        $str = $this->htmlSpecialChars(str_replace('\"', '"', base64_decode($str)));
        return $this->codeDecode($str, $image);
    }

    /**
     * This function gets allowed plugins from DB and loads them in the sanitizer
     *
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param int $id ID of the config
     * @param bool $allowimage Allow images?
     * @return object reference to the {@link IcmsConfig}
     */
    public function codeDecode_extended(string $text, bool $allowimage = true): string {
        $plugins = $this->pluginsConfig->sanitizerPlugins;
        if (!empty($plugins)) {
            foreach ($plugins as $item) {
                $text = $this->executeExtension($item, $text);
            }
        }
        return $text;
    }

    /**
     * loads the textsanitizer plugins
     *
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param string $name Name of the extension to load
     * @return bool
     */
    public function loadExtension($name) {
        $pluginsPath = $this->configuration->get('plugins_path', '');
        if (empty($name) || empty($pluginsPath) || !include_once $pluginsPath . "/textsanitizer/{$name}/{$name}.php") {
            return false;
        }
    }

    /**
     * Executes file with a certain extension using call_user_func_array
     *
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param string $name Name of the file to load
     * @param string $text Text to show if the function doesn't exist
     * @return array the return of the called function
     */
    public function executeExtension($name, $text) {
        $this->loadExtension($name);
        $func = "textsanitizer_{$name}";
        if (!function_exists($func)) {
            return $text;
        }
        $args = array_slice(func_get_args(), 1);
        return call_user_func_array($func, $args);
    }

    /**
     * Syntaxhighlight the code
     *
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param string $text purifies (lightly) and then syntax highlights the text
     * @return string $text the syntax highlighted text
     */
    public function textsanitizer_syntaxhighlight(string $text): string {
        return $text;
    }

    /**
     * Syntaxhighlight the code using PHP highlight
     *
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param string $text Text to highlight
     * @return string $buffer the highlighted text
     */
    public function textsanitizer_php_highlight($text) {
        // use PhpHighlight extension
    }

    /**
     * Syntaxhighlight the code using Geshi highlight
     *
     * @copyright (c) 2007-2010 The ImpressCMS Project - www.impresscms.org
     *
     * @param string $text The text to highlight
     * @return string $code the highlighted text
     */
    public function textsanitizer_geshi_highlight($text) {
        // uses sourcehighlight extension
    }

    /**
     * Trims certain text
     *
     * Replaces include/functions.php :: xoops_trim()
     *
     * @param string $text The Text to trim
     * @return string $text The trimmed text
     */
    public function icms_trim(string $text): string {
        if (function_exists('xoops_language_trim')) {
            return xoops_language_trim($text);
        }
        return trim($text);
    }

    /**
     * Function to reverse given text with utf-8 character sets
     *
     * credit for this function should goto lwc courtesy of php.net.
     *
     * @param string $str The text to be reversed.
     * @param bool $reverse true will reverse everything including numbers, false will reverse text only but numbers will be left intact.
     *        example: when true: impresscms 2008 > 8002 smcsserpmi, false: impresscms 2008 > 2008 smcsserpmi
     * @return string
     */
    public function utf8_strrev(string $str, bool $reverse = false): string {
        preg_match_all('/./us', $str, $ar);
        if ($reverse) {
            return implode('', array_reverse($ar[0]));
        } else {
            $temp = array();
            foreach ($ar[0] as $value) {
                if (is_numeric($value) && !empty($temp[0]) && is_numeric($temp[0])) {
                    foreach ($temp as $key => $value2) {
                        if (is_numeric($value2)) {
                            $pos = ($key + 1);
                        } else {
                            break;
                        }
                        $temp2 = array_splice($temp, $pos);
                        $temp = array_merge($temp, array($value), $temp2);
                    }
                } else {
                    array_unshift($temp, $value);
                }
            }
            return implode('', $temp);
        }
    }

    /**
     * Returns the portion of string specified by the start and length parameters.
     * If $trimmarker is supplied, it is appended to the return string.
     * This function works fine with multi-byte characters if mb_* functions exist on the server.
     *
     * Replaces legacy include/functions.php :: xoops_substr()
     *
     * @param string $str
     * @param int $start
     * @param int $length
     * @param string $trimmarker
     *
     * @return string
     */
    public function icms_substr(string $str, int $start, int $length, string $trimmarker = '...') {
        if ($this->multilangConfig->enable) {
            $tags = explode(',', $this->multilangConfig->tags);
            $strs = array();
            $hasML = false;
            foreach ($tags as $tag) {
                if (preg_match("/\[" . $tag . "](.*)\[\/" . $tag . "\]/sU", $str, $matches)) {
                    if (count($matches) > 0) {
                        $hasML = true;
                        $strs[] = $matches[1];
                    }
                }
            }
        } else {
            $hasML = false;
        }

        if (!$hasML) {
            $strs = array($str);
        }

        for ($i = 0; $i <= count($strs) - 1; $i++ ) {
            if (!$this->useMultiByte) {
                $strs[$i] = (strlen($strs[$i]) - $start <= $length) ? substr($strs[$i], $start, $length) : substr($strs[$i], $start, $length - strlen($trimmarker)) . $trimmarker;
            }
            if (function_exists('mb_internal_encoding') && @mb_internal_encoding($this->charset)) {
                $str2 = mb_strcut($strs[$i], $start, $length - strlen($trimmarker));
                $strs[$i] = $str2 . (mb_strlen($strs[$i]) !== mb_strlen($str2) ? $trimmarker : '');
            }

            $DEP_CHAR = 127;
            $pos_st = 0;
            $action = false;
            for ($pos_i = 0, $pos_iMax = strlen($strs[$i]); $pos_i < $pos_iMax; $pos_i++ ) {
                if (ord($strs[$i][$pos_i]) > 127) {
                    $pos_i++ ;
                }
                if ($pos_i <= $start) {
                    $pos_st = $pos_i;
                }
                if ($pos_i >= $pos_st + $length) {
                    $action = true;
                    break;
                }
            }
            $strs[$i] = ($action) ? substr($strs[$i], $pos_st, $pos_i - $pos_st - strlen($trimmarker)) . $trimmarker : $strs[$i];
            $strs[$i] = ($hasML) ? '[' . $tags[$i] . ']' . $strs[$i] . '[/' . $tags[$i] . ']' : $strs[$i];
        }
        return implode('', $strs);
    }
}