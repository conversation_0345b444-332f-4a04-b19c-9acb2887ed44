<?php

namespace Imponeer\DataFilter;

use HTMLPurifier;
use Imponeer\DataFilter\Configuration\PurifierConfig;

/**
 * Class to Clean & Filter HTML for various uses.
 * Class uses external HTML Purifier for filtering.
 *
 * @category	ICMS
 * @package		Core
 * @subpackage  Filters
 * @since		1.3
 * <AUTHOR> (<EMAIL>)
 * <AUTHOR> Project
 * @copyright	(c) 2007-2010 The ImpressCMS Project - www.impresscms.org
 * @version		$Id: HTMLFilter.php 12116 2012-11-18 22:08:37Z skenow $
 **/
/**
 *
 * HTML Purifier filters
 *
 * @category	ICMS
 * @package		Core
 *
 */
class HTMLFilter {

    /**
     * variable used by HTML Filter Library
     **/
    public $purifier;

    public function __construct(
        private readonly PurifierConfig $purifierConfig,
    )
    {
    }

// ----- Public Functions -----

    /**
     * Gets the selected HTML Filter & filters the content
     * @param    string  $html    input to be cleaned
     * @TODO	allow the webmasters to select which HTML Filter they want to use such as
     *			HTMLPurifier, HTMLLawed etc, for now we just have HTMLPurifier.
     * @return   string
     **/
    public function filterHTML(string $html): string {
        $fcomment = '<!-- filtered with htmlpurifier -->';

        $purified = strpos($html, $fcomment);
        if ($purified !== FALSE) {
            $html = str_replace($fcomment, '', $html);
        }

        if ($this->purifierConfig->enable) {
            if ($this->purifierConfig->filterExtractStyleBlocks) {
                require_once ICMS_PLUGINS_PATH . '/csstidy/class.csstidy.php';
            }
            // get the Config Data
            $icmsPurifyConf = $this->getHTMLFilterConfig();
            // uncomment for specific config debug info
            //parent::filterDebugInfo('icmsPurifyConf', $icmsPurifyConf);

            $purifier = new HTMLPurifier($icmsPurifyConf);
            $html = $purifier->purify($html);

            $html .= $fcomment;
        }

        return $html;
    }

// ----- Private Functions -----

    /*
     * Get list of current custom Filters & return them as objects in array
     * Custom Filters are located in libraries/htmlpurifier/standalone/HTMLPurifier/Filter/
     *
     * @return	object	array list of filter objects
     */
    private function getCustomFilterList() {
        $dirPath = ICMS_LIBRARIES_PATH . '/htmlpurifier/standalone/HTMLPurifier/Filter/';
        if ($this->purifierConfig->filterAllowCustom) {
            $filterList = array();

            $fileList = [];
            if (is_dir($dirPath)) {
                $files = glob($dirPath . '*.php');
                foreach ($files as $file) {
                    $fileList[basename($file)] = basename($file);
                }
            }
            unset($fileList['ExtractStyleBlocks.php'], $fileList['YouTube.php']);
            $fileList = array_values($fileList);

            foreach ($fileList as &$val) {
                $val = "HTMLPurifier_Filter_".substr($val, 0,strrpos($val,'.'));
                $newObject = new $val;
                $filterList[] = $newObject;
            }
        } else {
            $filterList = '';
        }

        return $filterList;
    }

    /**
     * Gets Custom Purifier configurations ** this function will improve in time **
     * @return  array    $icmsPurifierConf
     **/
    protected function getHTMLFilterConfig() {
        if ($this->purifierConfig->uriSafeIframeRegexp !== '') {
            $pos = strpos( $this->purifierConfig->uriSafeIframeRegexp, '|' );
            if ($pos === FALSE) {
                $IframeRegExp = '%^' . $this->purifierConfig->uriSafeIframeRegexp . '%';
            } else {
                $IframeRegExp = '%^(' . $this->purifierConfig->uriSafeIframeRegexp . ')%';
            }
        }

        $icmsPurifierConf = array(
            'HTML.DefinitionID' => $config->get('purifier_HTML_DefinitionID', 'default'),
            'HTML.DefinitionRev' => $config->get('purifier_HTML_DefinitionRev', 1),
            'HTML.Doctype' => $config->get('purifier_HTML_Doctype', 'HTML 4.01 Transitional'),
            'HTML.AllowedElements' => $config->get('purifier_HTML_AllowedElements', ''),
            'HTML.AllowedAttributes' => $config->get('purifier_HTML_AllowedAttributes', ''),
            'HTML.ForbiddenElements' => $config->get('purifier_HTML_ForbiddenElements', ''),
            'HTML.ForbiddenAttributes' => $config->get('purifier_HTML_ForbiddenAttributes', ''),
            'HTML.MaxImgLength' => $config->get('purifier_HTML_MaxImgLength', 1200),
            'HTML.TidyLevel' => $config->get('purifier_HTML_TidyLevel', 'medium'),
            'HTML.SafeEmbed' => $config->get('purifier_HTML_SafeEmbed', false),
            'HTML.SafeObject' => $config->get('purifier_HTML_SafeObject', false),
            'HTML.SafeIframe' => $config->get('purifier_HTML_SafeIframe', false),
            'HTML.Attr.Name.UseCDATA' => $config->get('purifier_HTML_AttrNameUseCDATA', false),
            'HTML.FlashAllowFullScreen' => $config->get('purifier_HTML_FlashAllowFullScreen', false),
            'Output.FlashCompat' => $config->get('purifier_Output_FlashCompat', false),
            'CSS.DefinitionRev' => $config->get('purifier_CSS_DefinitionRev', 1),
            'CSS.AllowImportant' => $config->get('purifier_CSS_AllowImportant', false),
            'CSS.AllowTricky' => $config->get('purifier_CSS_AllowTricky', false),
            'CSS.AllowedProperties' => $config->get('purifier_CSS_AllowedProperties', ''),
            'CSS.MaxImgLength' => $config->get('purifier_CSS_MaxImgLength', 1200),
            'CSS.Proprietary' => $config->get('purifier_CSS_Proprietary', false),
            'AutoFormat.AutoParagraph' => $config->get('purifier_AutoFormat_AutoParagraph', false),
            'AutoFormat.DisplayLinkURI' => $config->get('purifier_AutoFormat_DisplayLinkURI', false),
            'AutoFormat.Linkify' => $config->get('purifier_AutoFormat_Linkify', false),
            'AutoFormat.PurifierLinkify' => $config->get('purifier_AutoFormat_PurifierLinkify', false),
            'AutoFormat.Custom' => $config->get('purifier_AutoFormat_Custom', []),
            'AutoFormat.RemoveEmpty' => $config->get('purifier_AutoFormat_RemoveEmpty', false),
            'AutoFormat.RemoveEmpty.RemoveNbsp' => $config->get('purifier_AutoFormat_RemoveEmptyNbsp', false),
            'AutoFormat.RemoveEmpty.RemoveNbsp.Exceptions' => $config->get('purifier_AutoFormat_RemoveEmptyNbspExceptions', []),
            'Core.EscapeNonASCIICharacters' => $config->get('purifier_Core_EscapeNonASCIICharacters', false),
            'Core.HiddenElements' => $config->get('purifier_Core_HiddenElements', []),
            'Core.NormalizeNewlines' => $config->get('purifier_Core_NormalizeNewlines', false),
            'Core.RemoveInvalidImg' => $config->get('purifier_Core_RemoveInvalidImg', true),
            'Core.Encoding' => $config->get('charset', 'UTF-8'),
            'Cache.DefinitionImpl' => 'Serializer',
            'Cache.SerializerPath' => $config->get('cache_path', sys_get_temp_dir()) . '/htmlpurifier',
            'URI.Host' => $config->get('purifier_URI_Host', ''),
            'URI.Base' => $config->get('purifier_URI_Base', ''),
            'URI.Disable' => $config->get('purifier_URI_Disable', false),
            'URI.DisableExternal' => $config->get('purifier_URI_DisableExternal', false),
            'URI.DisableExternalResources' => $config->get('purifier_URI_DisableExternalResources', false),
            'URI.DisableResources' => $config->get('purifier_URI_DisableResources', false),
            'URI.MakeAbsolute' => $config->get('purifier_URI_MakeAbsolute', false),
            'URI.HostBlacklist' => $config->get('purifier_URI_HostBlacklist', []),
            'URI.AllowedSchemes' => $config->get('purifier_URI_AllowedSchemes', ['http' => true, 'https' => true, 'mailto' => true, 'ftp' => true]),
            'URI.DefinitionID' => $config->get('purifier_URI_DefinitionID', 'default'),
            'URI.DefinitionRev' => $config->get('purifier_URI_DefinitionRev', 1),
            'URI.SafeIframeRegexp' => $IframeRegExp,
            'Attr.AllowedFrameTargets' => $config->get('purifier_Attr_AllowedFrameTargets', ['_blank', '_self', '_target', '_top']),
            'Attr.AllowedRel' => $config->get('purifier_Attr_AllowedRel', ['nofollow', 'print']),
            'Attr.AllowedClasses' => $config->get('purifier_Attr_AllowedClasses', ''),
            'Attr.ForbiddenClasses' => $config->get('purifier_Attr_ForbiddenClasses', ''),
            'Attr.DefaultInvalidImage' => $config->get('purifier_Attr_DefaultInvalidImage', ''),
            'Attr.DefaultInvalidImageAlt' => $config->get('purifier_Attr_DefaultInvalidImageAlt', 'Invalid image'),
            'Attr.DefaultImageAlt' => $config->get('purifier_Attr_DefaultImageAlt', ''),
            'Attr.ClassUseCDATA' => $config->get('purifier_Attr_ClassUseCDATA', false),
            'Attr.IDPrefix' => $config->get('purifier_Attr_IDPrefix', ''),
            'Attr.EnableID' => $config->get('purifier_Attr_EnableID', false),
            'Attr.IDPrefixLocal' => $config->get('purifier_Attr_IDPrefixLocal', ''),
            'Attr.IDBlacklist' => $config->get('purifier_Attr_IDBlacklist', []),
            'Filter.ExtractStyleBlocks.Escaping' => $config->get('purifier_Filter_ExtractStyleBlocks_Escaping', true),
            'Filter.ExtractStyleBlocks.Scope' => $config->get('purifier_Filter_ExtractStyleBlocks_Scope', 'document'),
            'Filter.ExtractStyleBlocks' => $config->get('purifier_Filter_ExtractStyleBlocks', false),
            'Filter.YouTube' => $config->get('purifier_Filter_YouTube', false),
            'Filter.Custom' => self::getCustomFilterList(),
        );
        return self::cleanArray($icmsPurifierConf);
    }
}