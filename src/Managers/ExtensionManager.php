<?php

namespace Imponeer\DataFilter\Managers;

use Imponeer\DataFilter\Exceptions\ExtensionNotFoundException;
use Psr\Container\ContainerInterface;

class ExtensionManager implements ContainerInterface
{
    /**
     * @var array<string, object>
     */
    private array $extensions = [];

    /**
     * {@inheritdoc}
     */
    public function get(string $id): object
    {
        if (!$this->has($id)) {
            throw new ExtensionNotFoundException($id);
        }
        return $this->extensions[$id];
    }

    /**
     * {@inheritdoc}
     */
    public function has(string $id): bool
    {
        return isset($this->extensions[$id]);
    }
}
