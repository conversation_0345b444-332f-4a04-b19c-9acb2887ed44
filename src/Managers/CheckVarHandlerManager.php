<?php

namespace Imponeer\DataFilter\Managers;

use Imponeer\DataFilter\Exceptions\HandlerNotFoundException;
use Imponeer\DataFilter\Interfaces\HandlerInterface;
use Psr\Container\ContainerInterface;

class CheckVarHandlerManager implements ContainerInterface
{
    /**
     * @var array<string, string>
     */
    private array $handlerMapping = [];

    public function __construct(
        private readonly ContainerInterface $container
    ) {}

    /**
     * {@inheritdoc}
     */
    public function get(string $id): HandlerInterface
    {
        $class = $this->varHandlerTypeToClassName($id);

        if (!$this->container->has($id)) {
            throw new HandlerNotFoundException($id);
        }

        $item = $this->container->get($class);
        if (!$item instanceof HandlerInterface) {
            throw new HandlerNotFoundException($id);
        }

        return $item;
    }

    /**
     * {@inheritdoc}
     */
    public function has(string $id): bool
    {
        return $this->container->has(
            $this->varHandlerTypeToClassName($id)
        );
    }

    private function varHandlerTypeToClassName(string $varHandlerType): string
    {
        if (!isset($this->handlerMapping[$varHandlerType])) {
            $this->handlerMapping[$varHandlerType] = sprintf(
                "%s\\CheckVarHandlers\\%sHandler",
                substr(__NAMESPACE__, 0, strrpos(__NAMESPACE__, '\\')),
                ucfirst($varHandlerType)
            );
        }

        return $this->handlerMapping[$varHandlerType];
    }

}
