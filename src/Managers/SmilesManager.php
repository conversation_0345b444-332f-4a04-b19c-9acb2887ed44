<?php

namespace Imponeer\DataFilter\Managers;

use Imponeer\DataFilter\Configuration\SmilesConfig;
use Imponeer\DataFilter\DTO\SmileyDTO;

final class SmilesManager
{
    private array $allSmileys = [];
    private array $displaySmileys = [];

    public function __construct(
        public readonly SmilesConfig $smilesConfig, // // icms::$xoopsDB->query("SELECT * FROM " . icms::$xoopsDB->prefix('smiles')
    ) {}

    /**
     * Get the smileys
     *
     * @param bool $all
     * @return SmileyDTO[]
     */
    public function getSmileys(bool $all = false): array
    {
        if (count($this->allSmileys) === 0) {
            $allSmileys = [];
            $displaySmileys = [];
            foreach ($this->smilesConfig->smileys as $smiley) {
                if ($smiley->display) {
                    $displaySmileys[] = $smiley;
                }
                $allSmileys[] = $smiley;
            }
            $this->allSmileys = $allSmileys;
            $this->displaySmileys = $displaySmileys;
        }
        return $all ? $this->allSmileys : $this->displaySmileys;
    }

    /**
     * Replace emoticons in the message with smiley images
     *
     * @param string $message
     * @return string
     */
    public function replaceSmileys(string $message): string
    {
        $smileys = $this->getSmileys(true);
        foreach ($smileys as $smile) {
            $message = str_replace($smile->code, '<img src="' . $this->smilesConfig->smilePath . '/' . htmlspecialchars($smile->url) . '" alt="" />', $message);
        }
        return $message;
    }
}
