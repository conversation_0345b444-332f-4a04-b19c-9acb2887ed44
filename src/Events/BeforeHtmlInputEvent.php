<?php

namespace Imponeer\DataFilter\Events;

/**
 * Event triggered before HTML input filtering
 */
class BeforeHtmlInputEvent extends AbstractEvent
{
    public function __construct(
        string $content,
        public bool $smiley = true,
        public bool $icode = true,
        public bool $image = true,
        public bool $br = false
    ) {
        parent::__construct($content);
    }

    public function getEventName(): string
    {
        return 'beforeFilterHTMLinput';
    }
}
