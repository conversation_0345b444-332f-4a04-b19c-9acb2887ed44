<?php

namespace Imponeer\DataFilter\Events;

/**
 * Event triggered before textarea display filtering
 */
class BeforeTextareaDisplayEvent extends AbstractEvent
{
    public function __construct(
        string $content,
        public bool $smiley = true,
        public bool $icode = true,
        public bool $image = true,
        public bool $br = true
    ) {
        parent::__construct($content);
    }

    public function getEventName(): string
    {
        return 'beforeFilterTextareaDisplay';
    }
}
