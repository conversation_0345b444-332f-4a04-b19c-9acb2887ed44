<?php

namespace Imponeer\DataFilter\Events;

/**
 * Event triggered before HTML display filtering
 */
class BeforeHtmlDisplayEvent extends AbstractEvent
{
    public function __construct(
        string $content,
        public bool $icode = true,
        public bool $br = false
    ) {
        parent::__construct($content);
    }

    public function getEventName(): string
    {
        return 'beforeFilterHTMLdisplay';
    }

}
