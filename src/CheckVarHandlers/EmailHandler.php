<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Configuration\UserConfig;
use Imponeer\DataFilter\Interfaces\HandlerInterface;
use Imponeer\DataFilter\StopSpammer;

class <PERSON>ail<PERSON>andler implements HandlerInterface {

    public function __construct(
        private readonly StopSpammer $stopSpammer,
        private readonly UserConfig $userConfig,
    )
    {
    }

    public function autoconfigure(mixed &$options1, mixed &$options2): void {
        $valid_options1 = [0, 1];
        $valid_options2 = [0, 1];
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = 0;
        } else {
            $options1 = 1;
        }
        if ($options2 === '' || !in_array($options2, $valid_options2, true)) {
            $options2 = 0;
        } else {
            $options2 = 1;
        }
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        $data = filter_var($data, FILTER_SANITIZE_EMAIL);

        if (filter_var($data, FILTER_VALIDATE_EMAIL)) {
            if ($options2 && is_array($this->userConfig->badEmails)) {
                foreach ($this->userConfig->badEmails as $be) {
                    if ((!empty($be) && preg_match('/' . $be . '/i', $data))) {
                        return false;
                    }
                }
                if ($this->stopSpammer->badEmail($data)) {
                    return false;
                }
            }
        } else {
            return false;
        }
        if ($options1) {
            $data = str_replace(['@', '.'], [' at ', ' dot '], $data);
        }
        return $data;
    }
}
