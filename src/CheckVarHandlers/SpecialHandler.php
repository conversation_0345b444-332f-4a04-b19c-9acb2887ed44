<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Interfaces\HandlerInterface;

class SpecialHandler implements HandlerInterface {

    public function autoconfigure(mixed &$options1, mixed &$options2 = ''): void {
        $valid_options1 = ['striplow', 'striphigh', 'encodehigh'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = '';
        }
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        return match ($options1) {
            "striplow" => filter_var($data, FILTER_SANITIZE_SPECIAL_CHARS, FILTER_FLAG_STRIP_LOW),
            "striphigh" => filter_var($data, FILTER_SANITIZE_SPECIAL_CHARS, FILTER_FLAG_STRIP_HIGH),
            "encodehigh" => filter_var($data, FILTER_SANITIZE_SPECIAL_CHARS, FILTER_FLAG_ENCODE_HIGH),
            default => filter_var($data, FILTER_SANITIZE_SPECIAL_CHARS),
        };
    }
}
