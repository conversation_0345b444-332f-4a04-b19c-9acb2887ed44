<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Interfaces\HandlerInterface;
use Imponeer\DataFilter\DataFilter;

class TextHandler implements HandlerInterface {

    public function __construct(
        private readonly DataFilter $dataFilter,
    )
    {
    }

    public function autoconfigure(mixed &$options1, mixed &$options2 = ''): void {
        $valid_options1 = ['input', 'output', 'print'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = 'input';
        }
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        switch ($options1) {
            case 'output':
                return $this->dataFilter->filterTextareaDisplay($data);

            case 'print':
                return $data;

            case 'input':
            default:
                return $this->dataFilter->filterTextareaInput($data);
        }
    }
}
