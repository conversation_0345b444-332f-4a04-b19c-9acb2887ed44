<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Interfaces\HandlerInterface;

class <PERSON>p<PERSON><PERSON>ler implements HandlerInterface {

    public function autoconfigure(mixed &$options1, mixed &$options2 = ''): void {
        $valid_options1 = ['ipv4', 'ipv6', 'rfc', 'res'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = 'ipv4';
        }
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        return match ($options1) {
            "ipv4" => filter_var($data, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4),
            "ipv6" => filter_var($data, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6),
            "rfc" => filter_var($data, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE),
            "res" => filter_var($data, FILTER_VALIDATE_IP, FILTER_FLAG_NO_RES_RANGE),
            default => filter_var($data, FILTER_VALIDATE_IP),
        };
    }
}
