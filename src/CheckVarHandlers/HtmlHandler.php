<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Interfaces\HandlerInterface;
use Imponeer\DataFilter\DataFilter;

class HtmlHandler implements HandlerInterface {

    public function __construct(
        private readonly DataFilter $dataFilter,
    )
    {
    }

    public function autoconfigure(mixed &$options1, mixed &$options2 = ''): void {
        $valid_options1 = ['input', 'output', 'print', 'edit'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = 'input';
        }
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        switch ($options1) {
            case 'output':
                return $this->dataFilter->filterHTMLdisplay($data);

            case 'edit':
                $filtered = strpos($data, '<!-- input filtered -->');
                if ($filtered !== false) {
                    $data = str_replace(array('<!-- input filtered -->', '<!-- filtered with htmlpurifier -->'), '', $data);
                }
                return htmlspecialchars($data, ENT_QUOTES, $this->dataFilter->getCharset(), false);

            case 'print':
                // do nothing yet
                return $data;

            case 'input':
            default:
                return $this->dataFilter->filterHTMLinput($data);
        }
    }
}
