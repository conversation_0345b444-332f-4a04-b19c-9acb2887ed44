<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Interfaces\HandlerInterface;

class <PERSON>rl<PERSON><PERSON>ler implements HandlerInterface {
    public function autoconfigure(mixed &$options1, mixed &$options2): void {
        $valid_options1 = ['scheme', 'path', 'host', 'query'];
        $valid_options2 = [0, 1];
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = '';
        }
        if ($options2 === '' || !in_array($options2, $valid_options2, true)) {
            $options2 = 0;
        } else {
            $options2 = 1;
        }
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        $data = filter_var($data, FILTER_SANITIZE_URL);

        $valid = match ($options1) {
            "scheme" => filter_var($data, FILTER_VALIDATE_URL),
            "host" => filter_var($data, FILTER_VALIDATE_URL),
            "path" => filter_var($data, FILTER_VALIDATE_URL, FILTER_FLAG_PATH_REQUIRED),
            "query" => filter_var($data, FILTER_VALIDATE_URL, FILTER_FLAG_QUERY_REQUIRED),
            default => filter_var($data, FILTER_VALIDATE_URL),
        };
        if ($valid) {
            if (isset($options2) && $options2) {
                return filter_var($data, FILTER_SANITIZE_ENCODED);
            }
            return $data;
        }
        return false;
    }
}
