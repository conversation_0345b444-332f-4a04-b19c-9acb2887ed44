<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Interfaces\HandlerInterface;

class <PERSON>rHandler implements HandlerInterface {

    public function autoconfigure(mixed &$options1, mixed &$options2 = ''): void {
        $valid_options1 = ['noencode', 'striplow', 'striphigh', 'encodelow', 'encodehigh', 'encodeamp'];
        $options2 = '';
        if ($options1 === '' || !in_array($options1, $valid_options1, true)) {
            $options1 = '';
        }
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        // returns $string
        return match ($options1) {
            "noencode" => htmlspecialchars(strip_tags($data), ENT_NOQUOTES),
            "striplow" => filter_var(strip_tags($data), FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW),
            "striphigh" => filter_var(strip_tags($data), FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_HIGH),
            "encodelow" => filter_var(strip_tags($data), FILTER_UNSAFE_RAW, FILTER_FLAG_ENCODE_LOW),
            "encodehigh" => filter_var(strip_tags($data), FILTER_UNSAFE_RAW, FILTER_FLAG_ENCODE_HIGH),
            "encodeamp" => filter_var(strip_tags($data), FILTER_UNSAFE_RAW, FILTER_FLAG_ENCODE_AMP),
            default => htmlspecialchars(strip_tags($data), ENT_NOQUOTES),
        };
    }
}
