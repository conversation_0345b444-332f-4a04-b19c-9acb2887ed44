<?php
declare(strict_types=1);

namespace Imponeer\DataFilter\CheckVarHandlers;

use Imponeer\DataFilter\Interfaces\HandlerInterface;
use Imponeer\DataFilter\DataFilter;

class IntHandler implements HandlerInterface {

    public function autoconfigure(mixed &$options1, mixed &$options2): void {
        if (!is_int($options1) || !is_int($options2)) {
            $options1 = '';
            $options2 = '';
        } else {
            $options1 = (int) $options1;
            $options2 = (int) $options2;
        }
    }

    public function check(mixed $data, mixed $options1, mixed $options2): mixed {
        if (isset($options1, $options2) && is_int($options1) && is_int($options2)) {
            $option = ['options' => ['min_range' => $options1, 'max_range' => $options2]];

            return filter_var($data, FILTER_VALIDATE_INT, $option);
        }

        return filter_var($data, FILTER_VALIDATE_INT);
    }
}
