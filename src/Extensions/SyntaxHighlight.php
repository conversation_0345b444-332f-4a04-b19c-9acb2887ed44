<?php

namespace Imponeer\DataFilter\Extensions;

use Imponeer\DataFilter\Configuration\PluginsConfig;
use Imponeer\DataFilter\DataFilter;
use Imponeer\DataFilter\Interfaces\ExtensionInterface;

readonly class SyntaxHighlight implements ExtensionInterface
{
    public function __construct(
        private PluginsConfig   $pluginsConfig,
        private PhpHighlight    $phpHighlight,
        private GeshiHighlight  $geshiHighlight,
        private SourceHighlight $sourceHighlight,
        private DataFilter      $dataFilter,
    )
    {
    }

    public function __invoke(string $text): string
    {
        return match ($this->pluginsConfig->codeSanitizer) {
            'php' => ($this->phpHighlight)(
                $this->dataFilter->undoHtmlSpecialChars($text)
            ),
            'geshi' => sprintf("<code>%s</code>", ($this->geshiHighlight)(
                $this->dataFilter->undoHtmlSpecialChars($text)
            )),
            "highlighter" => ($this->sourceHighlight)(
                $this->dataFilter->undoHtmlSpecialChars($text)
            ),
            default => '<pre><code>' . $text . '</code></pre>',
        };
    }
}