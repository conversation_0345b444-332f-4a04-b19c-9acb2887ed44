<?php

namespace Imponeer\DataFilter\Extensions;

use Imponeer\DataFilter\Interfaces\ExtensionInterface;

class Php<PERSON><PERSON><PERSON> implements ExtensionInterface
{
    public function __invoke(string $text): string
    {
        $text = trim($text);

        [$text, $addedTagOpen, $addedTagClose] = $this->addMissingPhpTags($text);

        $buffer = $this->getHighlightedString($text);

        return $this->stripArtificialPhpTags($buffer, $addedTagOpen, $addedTagClose);
    }

    /**
     * @return array{0: string, 1: bool, 2: bool}
     */
    private function addMissingPhpTags(string $text): array
    {
        $addedTagOpen = false;
        $addedTagClose = false;

        if (!str_contains($text, '<?php') && !str_starts_with($text, '<?php')) {
            $text = "<?php\n" . $text;
            $addedTagOpen = true;
        }

        if (!str_contains($text, '?>')) {
            $text .= '?>';
            $addedTagClose = true;
        }

        return [$text, $addedTagOpen, $addedTagClose];
    }

    private function getHighlightedString(string $text): string
    {
        $highlighted = @highlight_string($text, true);

        if (!is_string($highlighted)) {
            return $this->fallbackHtmlEscape($text);
        }

        return $highlighted;
    }

    private function fallbackHtmlEscape(string $text): string
    {
        return htmlspecialchars($text, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
    }

    private function stripArtificialPhpTags(string $buffer, bool $addedTagOpen, bool $addedTagClose): string
    {
        $posOpen = $addedTagOpen ? strpos($buffer, '&lt;?php') : 0;
        $posClose = $addedTagClose ? strrpos($buffer, '?&gt;') : 0;

        $strOpen = $addedTagOpen ? substr($buffer, 0, $posOpen) : '';
        $strClose = $posClose ? substr($buffer, $posClose + 5) : '';

        $lengthOpen = $addedTagOpen ? $posOpen + 8 : 0;
        $lengthText = $posClose ? $posClose - $lengthOpen : 0;

        $strInternal = $lengthText
            ? substr($buffer, $lengthOpen, $lengthText)
            : substr($buffer, $lengthOpen);

        return $strOpen . $strInternal . $strClose;
    }
}