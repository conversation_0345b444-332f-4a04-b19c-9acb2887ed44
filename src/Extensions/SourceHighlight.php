<?php

namespace Imponeer\DataFilter\Extensions;

use Exception;
use Highlight\Highlighter;
use Imponeer\DataFilter\DataFilter;
use Imponeer\DataFilter\Interfaces\ExtensionInterface;

class SourceHighlight implements ExtensionInterface
{

    /**
     * @var string[]
     */
    private const array AUTODETECT_LANGUAGES = [
        'php',
        'javascript',
        'scss',
        'css',
        'html',
    ];

    public function __construct(
        private readonly Highlighter $highlighter,
        private readonly DataFilter  $dataFilter,
    )
    {
    }

    public function __invoke(string $text): string
    {
        $this->highlighter->setAutodetectLanguages(self::AUTODETECT_LANGUAGES);

        try {
            $highlighted = $this->highlighter->highlightAuto($text);
        } catch (Exception $exception) {
            return '<pre><code>' . $this->dataFilter->htmlSpecialChars($text) . '</code></pre>';
        }

        return sprintf(
            "<pre><code class=\"hljs %s\">%s</code></pre>",
            $highlighted->language,
            $highlighted->value
        );
    }
}